import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ReactNode, useMemo, useState } from "react";

import {
  TableBody,
  TableCell,
  TableHeader,
  TableHead,
  TableRow,
  Table,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { getCurrencyAbbreviations } from "@/constants/currencies";
import { Checkbox } from "../checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";

type ColumnDefWithCustomMeta<TData, TValue = unknown> = ColumnDef<
  TData,
  TValue
> & {
  meta?: {
    padding?: boolean;
    headerClassName?: string;
  };
};

type DataTableProps<TData> = {
  columns: ColumnDefWithCustomMeta<TData>[];
  data: (TData & { tooltip?: ReactNode })[];
  isPreview?: boolean;
  height?: string;
  rowSelection?: Record<string, boolean>;
  setRowSelection?: React.Dispatch<
    React.SetStateAction<Record<string, boolean>>
  >;
  showHeader?: boolean;
  selectSize?: number;
  headerClassName?: string;
  size?: "m" | "l";
  variant?: "default" | "alt1";
  emptyMessage?: React.ReactNode;
  currency?: string | null;
  tableType?: string;
};

export type DataTableMeta = {
  tooltip?: string | (() => React.ReactNode);
  isPreview?: boolean;
};

function TableTooltip({
  displayName,
  description,
  isPreview,
  currency,
  tableType: _tableType,
}: {
  displayName: string;
  description: string;
  isPreview?: boolean;
  currency?: string | null;
  tableType?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Popover open={isOpen}>
      <PopoverTrigger
        className="text-left"
        onMouseLeave={() => setIsOpen(false)}
        onMouseEnter={() => setIsOpen(true)}
      >
        {displayName.replace(
          "Currency Value",
          getCurrencyAbbreviations(currency)
        )}
      </PopoverTrigger>
      <PopoverContent
        side="bottom"
        align="start"
        className={cn(
          "w-fit flex-wrap whitespace-normal px-res-x-sm py-res-y-sm font-semibold",
          isPreview ? "w-fit max-w-[30vw] text-base" : "w-[40vw] text-2xl"
        )}
      >
        {description}
      </PopoverContent>
    </Popover>
  );
}

export function DataTable<TData>({
  columns,
  data,
  isPreview,
  height,
  rowSelection,
  setRowSelection,
  showHeader = true,
  selectSize = 50,
  headerClassName,
  size = "l",
  variant = "default",
  emptyMessage = "No data found",
  currency,
  tableType,
}: DataTableProps<TData>) {
  const table = useReactTable({
    data,
    columns: setRowSelection
      ? [
          {
            id: "select",
            size: selectSize,
            header: ({ table }) => (
              <Checkbox
                checked={table.getIsAllRowsSelected()}
                onCheckedChange={table.getToggleAllRowsSelectedHandler()}
              />
            ),
            cell: ({ row }) => (
              <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
                <Checkbox
                  className="border-2"
                  checked={row.getIsSelected()}
                  onCheckedChange={row.getToggleSelectedHandler()}
                />
              </div>
            ),
            enableSorting: false,
            enableColumnFilter: false,
          },
          ...columns,
        ]
      : columns,
    getCoreRowModel: getCoreRowModel(),
    state: rowSelection ? { rowSelection } : undefined,
    onRowSelectionChange: setRowSelection,
    enableRowSelection: !!setRowSelection,
    meta: {
      isPreview,
    },
  });

  const sizing = useMemo(() => {
    const sizeList = {
      m: {
        padding: "px-res-x-sm py-res-y-base",
        text: "text-base",
        header: "",
      },
      l: {
        padding: "px-res-x-sm py-res-y-base",
        text: "text-3xl",
        header: "h-[8vh]",
      },
    };

    return sizeList[size];
  }, [size]);

  const styling = useMemo(() => {
    const variantConfigList = {
      default: {
        header: "text-primary-500 border-none",
        headerCell: "text-[16px]",
        row: "",
        body: "",
      },
      alt1: {
        header: "text-white rounded-lg",
        headerCell: "bg-gradient",
        row: "",
        body: "",
      },
    };

    return variantConfigList[variant];
  }, [variant]);

  return (
    <div
      className="w-full overflow-y-auto rounded-b-xl"
      style={{ height, backgroundColor: "#f5f5f5" }}
    >
      <Table className="h-[1px] w-full table-fixed overflow-hidden">
        <TableHeader
          className={cn(
            "w-full",
            styling.header,
            sizing.text,
            showHeader ? sizing.header : "h-0 text-[0px] leading-[0]"
          )}
        >
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                const meta = header.column.columnDef.meta as
                  | DataTableMeta
                  | undefined;

                return (
                  <TableHead
                    className={cn(
                      "h-full items-start border-neutral-100 text-left font-bold",
                      styling.headerCell,
                      showHeader && `${sizing.padding}`,
                      headerClassName,
                      // Apply larger font size to ALL headers in preview mode for specific tables
                      isPreview &&
                        (tableType === "stakeholder_mapping" ||
                          tableType === "wallet_share" ||
                          tableType === "historic_revenue" ||
                          tableType === "circumstantial_analysis" ||
                          tableType === "missing_information" ||
                          tableType === "current_revenue" ||
                          tableType === "targeted_perception_development" ||
                          tableType === "current_opportunity" ||
                          tableType === "potential_opportunity" ||
                          tableType === "action_plan" ||
                          tableType === "top_action" ||
                          tableType === "insight_and_perspective" ||
                          tableType === "revenue_forecast" ||
                          tableType === "client_meeting_schedule" ||
                          tableType === "svot") &&
                        "text-4xl"
                    )}
                    key={header.id}
                    style={{
                      width: `${
                        isPreview &&
                        (tableType === "stakeholder_mapping" ||
                          tableType === "wallet_share" ||
                          tableType === "historic_revenue" ||
                          tableType === "circumstantial_analysis" ||
                          tableType === "missing_information" ||
                          tableType === "current_revenue" ||
                          tableType === "targeted_perception_development" ||
                          tableType === "current_opportunity" ||
                          tableType === "potential_opportunity" ||
                          tableType === "action_plan" ||
                          tableType === "top_action" ||
                          tableType === "insight_and_perspective" ||
                          tableType === "revenue_forecast" ||
                          tableType === "client_meeting_schedule" ||
                          tableType === "svot")
                          ? Math.round(header.getSize() * 1.6)
                          : header.getSize()
                      }px`,
                    }}
                  >
                    {typeof meta?.tooltip === "string" ? (
                      <TableTooltip
                        currency={currency}
                        isPreview={isPreview}
                        displayName={
                          (header.column.columnDef.header as string) ?? ""
                        }
                        description={meta.tooltip}
                        tableType={tableType}
                      />
                    ) : typeof meta?.tooltip === "function" ? (
                      <>{meta?.tooltip()}</>
                    ) : (
                      <>{header.column.columnDef.header}</>
                    )}
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>

        <TableBody className={cn("flex-auto", styling.body)}>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected?.() ? "selected" : undefined}
                className={cn("h-fit", sizing.text, styling.row)}
              >
                {row.getVisibleCells().map((cell) => {
                  const meta = (
                    cell.column.columnDef as ColumnDefWithCustomMeta<TData>
                  ).meta;

                  return (
                    <TableCell
                      className={cn(
                        "relative align-top",
                        cell.column.getIndex() <
                          table.getAllColumns().length - 1 && "border-r",
                        row.index < table.getRowModel().rows.length - 1 &&
                          "border-b",
                        meta?.padding ? sizing.padding : "p-0"
                      )}
                      style={{
                        ...(cell.column.getIndex() <
                          table.getAllColumns().length - 1 && {
                          borderRightColor: "#84858e",
                          borderRightWidth: "0.5px",
                        }),
                        ...(row.index < table.getRowModel().rows.length - 1 && {
                          borderBottomColor: "#84858e",
                          borderBottomWidth: "0.5px",
                        }),
                      }}
                      key={cell.id}
                    >
                      {flexRender(cell.column.columnDef.cell, {
                        ...cell.getContext(),
                      })}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))
          ) : (
            <TableRow className="w-full hover:bg-white">
              <TableCell
                colSpan={columns.length + (!!setRowSelection ? 1 : 0)}
                className="h-24 w-full bg-white text-center text-3xl"
              >
                {emptyMessage}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

export default DataTable;
